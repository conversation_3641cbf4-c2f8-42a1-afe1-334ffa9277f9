import { p as getConfigPage } from './requestDify.mjs';
import { t as transformArrayToObject, s as setItemWithTimestamp } from './commonJs.mjs';
import { i as useRequestEvent } from './server.mjs';
import 'axios';
import 'js-cookie';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'vue';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'vue-router';
import 'element-plus';
import 'vue/server-renderer';

const i18n_config = async () => {
  var _a;
  let messages;
  const event = useRequestEvent();
  const response = await getConfigPage("zh", event);
  if ((_a = response == null ? void 0 : response.list) == null ? void 0 : _a.length) {
    const cacheKey = "current_langs_pack";
    const langsStr = JSON.stringify(transformArrayToObject(response.list));
    messages = transformArrayToObject(response.list);
    setItemWithTimestamp(cacheKey, langsStr);
  }
  return {
    legacy: false,
    // 使用Composition API
    locale: "zh",
    // 默认语言
    fallbackLocale: "zh",
    messages
    // 使用API返回的消息
    // 其他配置...
  };
};

export { i18n_config as default };
//# sourceMappingURL=i18n.config.mjs.map
