const client_manifest = {
  "_Cc_AGWBm.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Cc_AGWBm.js",
    "name": "index",
    "imports": [
      "_gCCcCjuc.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_x_rD_Ya3.js"
    ],
    "css": [
      "index.BrTV2Fw4.css"
    ]
  },
  "index.BrTV2Fw4.css": {
    "file": "index.BrTV2Fw4.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_CnD89qOh.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CnD89qOh.js",
    "name": "ssr",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_DATDJ-AP.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DATDJ-AP.js",
    "name": "v3",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_DMAjf-Z3.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DMAjf-Z3.js",
    "name": "lang"
  },
  "_DVmeonZJ.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DVmeonZJ.js",
    "name": "qrcode",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "assets": [
      "kefu.B84d3bIM.png",
      "qrcode.DKObs6E2.png"
    ]
  },
  "kefu.B84d3bIM.png": {
    "file": "kefu.B84d3bIM.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "qrcode.DKObs6E2.png": {
    "file": "qrcode.DKObs6E2.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "_DlAUqK2U.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DlAUqK2U.js",
    "name": "_plugin-vue_export-helper"
  },
  "_e0SWII1p.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "e0SWII1p.js",
    "name": "index",
    "imports": [
      "_DVmeonZJ.js",
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": [
      "index.CNboY1JO.css"
    ]
  },
  "index.CNboY1JO.css": {
    "file": "index.CNboY1JO.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "_gCCcCjuc.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "gCCcCjuc.js",
    "name": "client-only",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_index.BrTV2Fw4.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "index.BrTV2Fw4.css",
    "src": "_index.BrTV2Fw4.css"
  },
  "_index.CNboY1JO.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "index.CNboY1JO.css",
    "src": "_index.CNboY1JO.css"
  },
  "_x_rD_Ya3.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "x_rD_Ya3.js",
    "name": "interval"
  },
  "assets/imgs/Bitmap.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "Bitmap.BV-s4dwZ.png",
    "src": "assets/imgs/Bitmap.png"
  },
  "assets/imgs/kefu.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "kefu.B84d3bIM.png",
    "src": "assets/imgs/kefu.png"
  },
  "assets/imgs/qrcode.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "qrcode.DKObs6E2.png",
    "src": "assets/imgs/qrcode.png"
  },
  "assets/imgs/right.png": {
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png",
    "file": "right.C7PU2hTH.png",
    "src": "assets/imgs/right.png"
  },
  "i18n.config.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BEufjh9X.js",
    "name": "i18n.config",
    "src": "i18n.config.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/default.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CmZtZl0_.js",
    "name": "default",
    "src": "layouts/default.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "default.q2gX9DjU.css": {
    "file": "default.q2gX9DjU.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "layouts/tools.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C8-myDC5.js",
    "name": "tools",
    "src": "layouts/tools.vue",
    "isDynamicEntry": true,
    "imports": [
      "_gCCcCjuc.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_CnD89qOh.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_e0SWII1p.js",
      "_DVmeonZJ.js"
    ],
    "css": []
  },
  "tools.x4A8XtZE.css": {
    "file": "tools.x4A8XtZE.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/highlight.js/es/index.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "8UeBS2gq.js",
    "name": "index",
    "src": "node_modules/highlight.js/es/index.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/highlight.js/styles/vs2015.css": {
    "resourceType": "style",
    "prefetch": true,
    "preload": true,
    "file": "vs2015.LLWOf3w5.css",
    "src": "node_modules/highlight.js/styles/vs2015.css"
  },
  "node_modules/nuxt/dist/app/components/error-404.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DZrXWHiV.js",
    "name": "error-404",
    "src": "node_modules/nuxt/dist/app/components/error-404.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_DATDJ-AP.js"
    ],
    "css": []
  },
  "error-404.aNCZ2L4y.css": {
    "file": "error-404.aNCZ2L4y.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/components/error-500.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CMI2BNYb.js",
    "name": "error-500",
    "src": "node_modules/nuxt/dist/app/components/error-500.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "_DATDJ-AP.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "error-500.JESWioAZ.css": {
    "file": "error-500.JESWioAZ.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Djf8yQrJ.js",
    "name": "entry",
    "src": "node_modules/nuxt/dist/app/entry.js",
    "isEntry": true,
    "isDynamicEntry": true,
    "dynamicImports": [
      "i18n.config.js",
      "layouts/default.vue",
      "layouts/tools.vue",
      "node_modules/nuxt/dist/app/components/error-404.vue",
      "node_modules/nuxt/dist/app/components/error-500.vue"
    ],
    "css": [
      "entry.BM64DQIl.css"
    ],
    "_globalCSS": true
  },
  "entry.BM64DQIl.css": {
    "file": "entry.BM64DQIl.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/article/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DKdt-pS5.js",
    "name": "_id_",
    "src": "pages/article/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CnD89qOh.js",
      "_DATDJ-AP.js",
      "_DlAUqK2U.js"
    ],
    "dynamicImports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "_id_.DmbhpAJy.css": {
    "file": "_id_.DmbhpAJy.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/cases/[caseId].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ZTQAMEWt.js",
    "name": "_caseId_",
    "src": "pages/cases/[caseId].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js",
      "_DATDJ-AP.js",
      "_CnD89qOh.js"
    ],
    "dynamicImports": [
      "node_modules/highlight.js/es/index.js"
    ],
    "css": []
  },
  "_caseId_.CmqoPyH7.css": {
    "file": "_caseId_.CmqoPyH7.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/chat/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ZhiTAosv.js",
    "name": "_appUuid_",
    "src": "pages/chat/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_DVmeonZJ.js",
      "_DMAjf-Z3.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_CnD89qOh.js",
      "_DATDJ-AP.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_appUuid_.BGANLTBm.css": {
    "file": "_appUuid_.BGANLTBm.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "uJKSG1VL.js",
    "name": "index",
    "src": "pages/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "_gCCcCjuc.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_CnD89qOh.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js",
      "_e0SWII1p.js",
      "_Cc_AGWBm.js",
      "_DMAjf-Z3.js",
      "_DATDJ-AP.js",
      "_DVmeonZJ.js"
    ],
    "css": [],
    "assets": [
      "Bitmap.BV-s4dwZ.png"
    ]
  },
  "index.YYgUUJq8.css": {
    "file": "index.YYgUUJq8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "Bitmap.BV-s4dwZ.png": {
    "file": "Bitmap.BV-s4dwZ.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  },
  "pages/login/[socialType].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BnhMaQ66.js",
    "name": "_socialType_",
    "src": "pages/login/[socialType].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_socialType_.DYGiZ1nj.css": {
    "file": "_socialType_.DYGiZ1nj.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/login/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D7VDVQr1.js",
    "name": "index",
    "src": "pages/login/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "index.D0WfnBpn.css": {
    "file": "index.D0WfnBpn.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/payLink/[payInfo].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "YrxB9qkb.js",
    "name": "_payInfo_",
    "src": "pages/payLink/[payInfo].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_payInfo_.BM6XeFv8.css": {
    "file": "_payInfo_.BM6XeFv8.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/sign-up.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "InIR5M6-.js",
    "name": "sign-up",
    "src": "pages/sign-up.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "sign-up.Cj3pzlL2.css": {
    "file": "sign-up.Cj3pzlL2.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DYyUialU.js",
    "name": "_appUuid_",
    "src": "pages/tool/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_gCCcCjuc.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_Cc_AGWBm.js",
      "pages/tool/components/InputField.vue",
      "_DMAjf-Z3.js",
      "_CnD89qOh.js",
      "_DATDJ-AP.js",
      "_x_rD_Ya3.js",
      "_DlAUqK2U.js"
    ],
    "css": []
  },
  "_appUuid_.DaDP-19O.css": {
    "file": "_appUuid_.DaDP-19O.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/components/InputField.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "FLrENlXw.js",
    "name": "InputField",
    "src": "pages/tool/components/InputField.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/tool/destroy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "TdxoFChm.js",
    "name": "destroy",
    "src": "pages/tool/destroy.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "destroy.BvPgb2nQ.css": {
    "file": "destroy.BvPgb2nQ.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/tool/privacy-policy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ij-UMZCH.js",
    "name": "privacy-policy",
    "src": "pages/tool/privacy-policy.vue",
    "isDynamicEntry": true,
    "imports": [
      "_DlAUqK2U.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/write/[appUuid].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dz1pKaYv.js",
    "name": "_appUuid_",
    "src": "pages/write/[appUuid].vue",
    "isDynamicEntry": true,
    "imports": [
      "_DVmeonZJ.js",
      "node_modules/nuxt/dist/app/entry.js",
      "_DMAjf-Z3.js",
      "_CnD89qOh.js",
      "_DATDJ-AP.js",
      "_DlAUqK2U.js"
    ],
    "css": [],
    "assets": [
      "right.C7PU2hTH.png"
    ]
  },
  "_appUuid_.CUY9OiQN.css": {
    "file": "_appUuid_.CUY9OiQN.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "right.C7PU2hTH.png": {
    "file": "right.C7PU2hTH.png",
    "resourceType": "image",
    "prefetch": true,
    "mimeType": "image/png"
  }
};

export { client_manifest as default };
//# sourceMappingURL=client.manifest.mjs.map
