const getStorage = () => {
  return {
    setItem: () => {
    },
    getItem: () => null,
    removeItem: () => {
    },
    clear: () => {
    }
  };
};
const STORAGE = getStorage();
const storage = {
  set: (key, val) => {
    if (typeof val !== "string") {
      val = JSON.stringify(val);
    }
  },
  get: (key) => {
    return STORAGE.getItem(key);
  },
  remove: (key) => {
  },
  clear: () => {
  }
};

const languages = {
  en: "英文",
  "zh": "中文",
  pt: "葡萄牙语",
  ar: "阿拉伯语",
  "tw": "繁体中文",
  id: "印尼语",
  ja: "日语",
  ko: "韩语",
  vi: "越南语",
  ms: "马来语",
  es: "西班牙语"
};
const appTypes = {
  工具: "market.tool",
  写作: "market.write",
  问答: "market.chat",
  科研工具: "market.researchTool",
  学术写作: "market.academicWrite",
  医学会话: "market.medicalChat",
  全部: "market.all",
  我的应用: "market.myApp"
};
const getDefaultLanguageCode = (locales) => {
  const cookie = locales;
  const browserLang = "en";
  return cookie || browserLang || "en";
};
const defaultLanguageName = (locales) => {
  const langCode = getDefaultLanguageCode(locales);
  return languages[langCode] || languages["en"];
};
const transformArrayToObject = (array) => {
  const result = {};
  array.forEach((item) => {
    const [langCode] = item.key.split(".");
    const value = JSON.parse(item.value);
    if (!result[langCode]) {
      result[langCode] = {};
    }
    result[langCode] = { ...result[langCode], ...value };
  });
  return result;
};
const setItemWithTimestamp = (key, value) => {
  const timestamp = Date.now();
  storage.set(key + "_value", value);
  storage.set(key + "_timestamp", timestamp);
};

export { appTypes as a, defaultLanguageName as d, getDefaultLanguageCode as g, languages as l, setItemWithTimestamp as s, transformArrayToObject as t };
//# sourceMappingURL=commonJs.mjs.map
