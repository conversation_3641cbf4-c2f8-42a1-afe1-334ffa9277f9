import { _ as __nuxt_component_0 } from './client-only.mjs';
import { reactive, ref, withAsyncContext, computed, watch, resolveComponent, unref, withCtx, createTextVNode, toDisplayString, isRef, createBlock, openBlock, Fragment, renderList, createVNode, createCommentVNode, useSSRContext } from 'vue';
import { ssrRenderStyle, ssrRenderList, ssrRenderComponent, ssrInterpolate, ssrRenderClass, ssrRenderAttr } from 'vue/server-renderer';
import cookie from 'js-cookie';
import { SuccessFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { a as getPackageByKey, h as getAppByUuid, i as getParameters, e as createSubscription, C as Cookies, m as mainLogin, j as getAppPrompt } from './requestDify.mjs';
import { g as getAssetsFile, p as pay, a as payMobile } from './index.mjs';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import _sfc_main$1 from './InputField.vue.mjs';
import { Tabs, Tab, Popup } from 'vant';
import { h as home, a as appuuid } from './lang.mjs';
import { g as getDefaultLanguageCode, d as defaultLanguageName } from './commonJs.mjs';
import { f as useRoute, e as useI18n, u as useRouter, g as useCookie, i as useRequestEvent } from './server.mjs';
import { u as useAsyncData } from './asyncData.mjs';
import { u as useHead } from './v3.mjs';
import { s as setInterval } from './interval.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import 'axios';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'vue-router';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'unhead/plugins';
import 'unhead/utils';
import 'devalue';

const _sfc_main = {
  __name: "[appUuid]",
  __ssrInlineRender: true,
  props: {
    currentItem: {
      type: Object,
      default: () => {
      }
    },
    subStatusDetail: {
      type: Object,
      default: () => {
      }
    }
  },
  async setup(__props, { expose: __expose }) {
    var _a, _b, _c, _d, _e, _f, _g;
    let __temp, __restore;
    const props = __props;
    const loadingImg = getAssetsFile("loading.png");
    const copy = getAssetsFile("copy.png");
    const inputs = reactive({});
    const route = useRoute();
    const keyInfo = {};
    const userInputForm = ref([]);
    const subStatusDetail = ref(props.subStatusDetail);
    const currentItem = ref(props.currentItem);
    const userInfo = ref(
      cookie.get("userInfo") ? JSON.parse(cookie.get("userInfo")) : {}
    );
    const nodeInfo = ref(null);
    const { t, locale } = useI18n();
    useRouter();
    const longText = ref(false);
    let activeName = ref("a");
    let isdisabled = ref(true);
    let answer = ref("");
    const task_id = ref("");
    const childRef = ref(null);
    const childRefs = ref(null);
    const activeNames = ref(["1", "2"]);
    const loadingDecImg = ref(false);
    const isShow = ref(false);
    const payShow = ref(false);
    const dAppUuid = ref("");
    const title = ref("");
    const appUuid = ref("");
    const ai_apps_lang = useCookie("ai_apps_lang", { domain: ".medon.com.cn", maxAge: 30 * 24 * 60 * 60 * 12 });
    const subScript = async () => {
      if (locale.value == "zh") {
        subStatusDetail.value = await getPackageByKey();
        currentItem.value = props.currentItem;
        payShow.value = true;
      } else {
        payShow.value = true;
      }
    };
    __expose({
      subScript
    });
    const close = () => {
      payShow.value = false;
    };
    const subscribe = async (item, appUuid2) => {
      var _a2;
      let language = getDefaultLanguageCode(locale.value);
      if (!((_a2 = userInfo.value) == null ? void 0 : _a2.userId)) {
        if (!language || language == "zh") {
          (void 0).addLoginDom();
        } else {
          (void 0).href = (void 0).origin + "/" + locale.value + "/login";
        }
      } else {
        const subscriptionParams = {
          appUuid: appUuid2,
          priceId: item.priceId,
          monthNum: item.monthNum
        };
        let res = await createSubscription(subscriptionParams);
        if (res) {
          ElMessage({
            type: "success",
            message: t("tool.sS")
          });
          setTimeout(() => {
            (void 0).href = res;
          }, 1e3);
        }
      }
    };
    const { data: parameters } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData(
      "parameters",
      async () => {
        var _a2;
        const userInfo2 = useCookie("userInfo");
        ai_apps_lang.value = locale.value;
        const event = useRequestEvent();
        const appLang = await getAppByUuid(route.params.appUuid, event);
        dAppUuid.value = appLang == null ? void 0 : appLang.dAppUuid;
        title.value = `${appLang == null ? void 0 : appLang.appName}-${appuuid[ai_apps_lang.value]}`;
        const res = await getParameters(
          {
            appId: dAppUuid.value,
            user: (_a2 = userInfo2 == null ? void 0 : userInfo2.value) == null ? void 0 : _a2.userName
          },
          event
        );
        return res;
      },
      { server: true }
    )), __temp = await __temp, __restore(), __temp);
    if ((_a = parameters.value) == null ? void 0 : _a.user_input_form) {
      userInputForm.value = parameters.value.user_input_form;
      parameters.value.user_input_form.forEach((item) => {
        const key = Object.keys(item)[0];
        const variable = item[key].variable;
        keyInfo[variable] = {
          label: item[key].label
        };
        inputs[variable] = "";
      });
    }
    useHead({
      title: title.value,
      meta: [
        {
          name: "keywords",
          content: `${home["toolKeywords"][ai_apps_lang.value]},${(_b = props == null ? void 0 : props.currentItem) == null ? void 0 : _b.appName}`
        },
        {
          name: "description",
          content: (_c = props == null ? void 0 : props.currentItem) == null ? void 0 : _c.appDescription
        },
        { property: "og:type", content: "website" },
        { property: "og:title", content: title.value },
        { property: "og:description", content: (_d = props == null ? void 0 : props.currentItem) == null ? void 0 : _d.appDescription },
        { property: "og:image", content: (_e = props == null ? void 0 : props.currentItem) == null ? void 0 : _e.appIcon },
        { name: "twitter:card", content: "summary_large_image" },
        // 注意：根据常见用法推断 content 为 'summary_large_image'
        { name: "twitter:title", content: title.value },
        { name: "twitter:description", content: (_f = props == null ? void 0 : props.currentItem) == null ? void 0 : _f.appDescription },
        { name: "twitter:image", content: (_g = props == null ? void 0 : props.currentItem) == null ? void 0 : _g.appIcon }
      ]
    });
    const onClickTab = (e) => {
      activeName.value = e.name;
    };
    const handleOrder = async () => {
      payShow.value = true;
    };
    const isNormal = computed(() => {
      return !!userInputForm.value.length;
    });
    const requiredKeyArr = computed(() => {
      return userInputForm.value.filter((item) => {
        const key = Object.keys(item)[0];
        return item[key].required === true;
      }).map((item) => {
        const key = Object.keys(item)[0];
        return item[key].variable;
      });
    });
    const getTemplateInfo = () => {
      if (!dAppUuid.value) return;
      getAppPrompt({
        appId: dAppUuid.value,
        user: userInfo.value.userName
      }).then((res) => {
        nodeInfo.value = { ...res };
      });
    };
    const loading = ref(false);
    const loadingBtn = ref(false);
    const loadingPng = ref(false);
    const isCopy = ref(false);
    const payShowChange = (value) => {
      payShow.value = value;
    };
    const hasUndefinedValue = (inputs2) => {
      return Object.values(inputs2).some((value) => value);
    };
    const onExec = async () => {
      var _a2, _b2, _c2, _d2, _e2, _f2;
      if (!cookie.get("userInfo")) {
        cookie.remove("yudaoToken", { domain: "ai.medsci.cn" });
        cookie.remove("yudaoToken", { domain: "ai.medon.com.cn" });
        cookie.remove("yudaoToken", { domain: ".medsci.cn" });
        cookie.remove("yudaoToken", { domain: ".medon.com.cn" });
        cookie.remove("yudaoToken", { domain: "localhost" });
        localStorage.removeItem("hasuraToken");
        const language = getDefaultLanguageCode(locale.value);
        if (!language || language == "zh") {
          (void 0).addLoginDom();
        } else {
          (void 0).href = (void 0).origin + "/" + locale.value + "/login";
        }
        return;
      }
      await loginMain();
      if (!((_b2 = (_a2 = props.currentItem) == null ? void 0 : _a2.appUser) == null ? void 0 : _b2.status) || ((_d2 = (_c2 = props.currentItem) == null ? void 0 : _c2.appUser) == null ? void 0 : _d2.status) == 2) {
        payShow.value = true;
        return false;
      }
      if (requiredKeyArr.value.length == 0 && !hasUndefinedValue(inputs)) {
        ElMessage({
          message: `${t("tool.enterquestion")}`,
          type: "error"
        });
        return;
      }
      for (let key in inputs) {
        if (requiredKeyArr.value.includes(key) && !inputs[key]) {
          ElMessage({
            message: `${keyInfo[key].label}${t("tool.requiredfield")}`,
            type: "error"
          });
          return;
        }
      }
      if ((_e2 = nodeInfo.value) == null ? void 0 : _e2.mode) {
        if (["advanced-chat", "chat"].includes((_f2 = nodeInfo.value) == null ? void 0 : _f2.mode)) {
          ElMessage({
            type: "success",
            message: t("tool.planning")
          });
        } else if (nodeInfo.value.mode == "completion") {
          isdisabled.value = false;
          setTimeout(() => {
            activeName.value = "b";
          }, 1e3);
          submitCompletionflow();
        } else {
          isdisabled.value = false;
          setTimeout(() => {
            activeName.value = "b";
          }, 1e3);
          submitWorkflow();
        }
      }
    };
    const typingQueue = ref([]);
    var decList = ref([]);
    var decListNew = ref([]);
    const process = ref("");
    const index = ref(0);
    const displayedText = ref("");
    const processLoadingPng = ref(false);
    const isSubscribe = ref(false);
    let currentIndex = ref(0);
    const isResult = ref(false);
    const isTyping = ref(false);
    let isWorkflowFinished = false;
    let isTypingFinished = false;
    let ctrl;
    watch(
      decList,
      () => {
        processNextElement();
      },
      {
        deep: true
      }
    );
    const processNextElement = () => {
      if (currentIndex.value < decList.value.length) {
        decListNew.value.push(decList.value[currentIndex.value]);
        currentIndex.value++;
        setTimeout(processNextElement, 1e3);
      }
    };
    const typeWriter = () => {
      if (index.value < process.value.length) {
        processLoadingPng.value = true;
        displayedText.value += process.value.charAt(index.value);
        index.value++;
        setTimeout(typeWriter, 20);
      } else {
        isTyping.value = false;
        processLoadingPng.value = false;
        loadingPng.value = true;
        processTypingQueue();
      }
    };
    const submitWorkflow = async () => {
      isShow.value = true;
      loadingDecImg.value = true;
      answer.value = "";
      decList.value = [];
      decListNew.value = [];
      currentIndex.value = 0;
      displayedText.value = "";
      process.value = "";
      typingQueue.value = [];
      isResult.value = false;
      isWorkflowFinished = false;
      index.value = 0;
      ctrl = new AbortController();
      try {
        let url = `${(void 0).location.origin}/dev-api/ai-base/v1/workflows/run`;
        if (false) ;
        loading.value = true;
        loadingBtn.value = true;
        isCopy.value = false;
        await fetchEventSource(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("yudaoToken") || null}`
          },
          body: JSON.stringify({
            appId: dAppUuid.value,
            user: userInfo.value.userName,
            inputs: {
              ...inputs,
              outputLanguage: inputs.outputLanguage ? inputs.outputLanguage : defaultLanguageName(locale.value) == "中文" ? "简体中文" : defaultLanguageName(locale.value)
            },
            requestId: crypto.randomUUID(),
            files: [],
            response_mode: "streaming",
            appUuid: appUuid.value
          }),
          // AbortSignal
          onmessage(event) {
            var _a2, _b2, _c2, _d2, _e2, _f2, _g2, _h, _i, _j;
            if (event.data.trim()) {
              try {
                const data = JSON.parse(event.data);
                task_id.value = data.task_id;
                if (data.error) {
                  throw new Error(data.error);
                }
                if (((_a2 = data == null ? void 0 : data.data) == null ? void 0 : _a2.title) == "智能体推理思维链" && data.event === "node_finished") {
                  isTyping.value = true;
                  process.value = (_d2 = JSON.parse((_c2 = (_b2 = data == null ? void 0 : data.data) == null ? void 0 : _b2.outputs) == null ? void 0 : _c2.text)) == null ? void 0 : _d2.text;
                  typeWriter();
                }
                if (data.event === "node_started" && !isResult.value && ((_e2 = data == null ? void 0 : data.data) == null ? void 0 : _e2.title) != "开始") {
                  decList.value.push({
                    node_id: (_f2 = data == null ? void 0 : data.data) == null ? void 0 : _f2.node_id,
                    title: (_g2 = data == null ? void 0 : data.data) == null ? void 0 : _g2.title,
                    status: false
                  });
                }
                if (data.event === "error") {
                  if (data.code == 5052) {
                    setTimeout(() => {
                      ctrl.abort();
                    }, 0);
                    loadingDecImg.value = false;
                    loading.value = false;
                    loadingPng.value = false;
                    loadingBtn.value = false;
                    isTyping.value = false;
                    ElMessage.error(data.message);
                    return;
                  }
                  if (data.code == 5047) {
                    isSubscribe.value = true;
                  }
                  isResult.value = true;
                  loadingDecImg.value = false;
                  isWorkflowFinished = true;
                  loadingBtn.value = false;
                  answer.value = data == null ? void 0 : data.message;
                }
                if (data.event === "node_finished") {
                  decList.value.forEach((element) => {
                    var _a3;
                    if (element.node_id == ((_a3 = data == null ? void 0 : data.data) == null ? void 0 : _a3.node_id)) {
                      element.status = true;
                    }
                  });
                }
                if (data.event === "text_chunk") {
                  longText.value = true;
                  typingQueue.value.push((_h = data == null ? void 0 : data.data) == null ? void 0 : _h.text);
                  if (!isTyping.value) {
                    processTypingQueue();
                  }
                }
                if (data.event === "workflow_started") {
                  loading.value = false;
                }
                if (data.event === "workflow_finished") {
                  isWorkflowFinished = true;
                  isResult.value = true;
                  loadingDecImg.value = false;
                  loadingBtn.value = false;
                  loadingPng.value = false;
                  if (!longText.value) {
                    typingQueue.value.push((_j = (_i = data == null ? void 0 : data.data) == null ? void 0 : _i.outputs) == null ? void 0 : _j.text);
                    if (!isTyping.value) {
                      processTypingQueue();
                    }
                  }
                }
              } catch (error) {
                handleError(error);
              }
            }
          },
          onerror(error) {
            handleError(error);
          },
          signal: ctrl.signal,
          openWhenHidden: true
        });
      } catch (error) {
        handleError();
      }
    };
    const submitCompletionflow = async () => {
      answer.value = "";
      typingQueue.value = [];
      isTyping.value = false;
      isWorkflowFinished = false;
      ctrl = new AbortController();
      try {
        let url = `${(void 0).location.origin}/dev-api/ai-base/v1/completion-messages`;
        if (false) ;
        loading.value = true;
        loadingBtn.value = true;
        isCopy.value = false;
        await fetchEventSource(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("yudaoToken") || null}`
          },
          body: JSON.stringify({
            appId: dAppUuid.value,
            user: userInfo.value.userName,
            inputs: {
              ...inputs,
              outputLanguage: defaultLanguageName()
            },
            files: [],
            response_mode: "streaming",
            appUuid: appUuid.value,
            requestId: crypto.randomUUID()
          }),
          onmessage(event) {
            loading.value = false;
            loadingPng.value = true;
            if (event.data.trim()) {
              try {
                const data = JSON.parse(event.data);
                task_id.value = data.task_id;
                if (data.error) {
                  throw new Error(data.error);
                }
                if (data.event === "error") {
                  if (data.code == 5052) {
                    setTimeout(() => {
                      ctrl.abort();
                    }, 0);
                    loadingDecImg.value = false;
                    loading.value = false;
                    loadingPng.value = false;
                    loadingBtn.value = false;
                    isTyping.value = false;
                    ElMessage.error(data.message);
                    return;
                  }
                  if (data.code == 5047) {
                    isSubscribe.value = true;
                  }
                  isWorkflowFinished = true;
                  answer.value = data == null ? void 0 : data.message;
                  loadingPng.value = false;
                  loadingBtn.value = false;
                }
                if (data.event === "message") {
                  typingQueue.value.push(data == null ? void 0 : data.answer);
                  if (!isTyping.value) {
                    processTypingQueue();
                  }
                }
                if (data.event === "message_end") {
                  isWorkflowFinished = true;
                  loadingBtn.value = false;
                  loadingPng.value = false;
                }
              } catch (error) {
                handleError(error);
              }
            }
          },
          onerror(error) {
            handleError(error);
          },
          signal: ctrl.signal,
          openWhenHidden: true
        });
      } catch (error) {
        handleError();
      }
    };
    const processTypingQueue = () => {
      if (typingQueue.value.length === 0) {
        isTyping.value = false;
        isTypingFinished = true;
        checkCopyButton();
        return;
      }
      isTyping.value = true;
      typingQueue.value.shift();
      simulateTyping().then(() => {
        processTypingQueue();
      });
    };
    const checkCopyButton = () => {
      if (isTypingFinished && isWorkflowFinished) {
        loadingBtn.value = false;
        loadingPng.value = false;
        isCopy.value = true;
      }
    };
    const simulateTyping = (text) => {
      return new Promise((resolve) => {
        setInterval();
      });
    };
    const handleError = () => {
      setTimeout(() => {
        ctrl.abort();
      }, 0);
      loadingDecImg.value = false;
      loading.value = false;
      loadingPng.value = false;
      loadingBtn.value = false;
      isTyping.value = false;
      ElMessage.error(t("tool.accessbusy"));
      answer.value = t("tool.accessbusy");
    };
    const reset = () => {
      for (let key in inputs) {
        inputs[key] = "";
      }
      childRef.value.forEach((element) => {
        element.updateMessage();
      });
      childRefs.value.forEach((element) => {
        element.updateMessage();
      });
    };
    const loginMain = async () => {
      const token = Cookies.get("yudaoToken");
      if (token) {
        getTemplateInfo();
        return;
      }
      try {
        await mainLogin({
          userId: userInfo.value.userId,
          userName: userInfo.value.userName,
          realName: userInfo.value.realName,
          avatar: userInfo.value.avatar,
          plaintextUserId: userInfo.value.plaintextUserId,
          mobile: userInfo.value.mobile,
          email: userInfo.value.email
        }).then(async (res) => {
          if ((res == null ? void 0 : res.token) && (res == null ? void 0 : res.htoken)) {
            Cookies.set("yudaoToken", res.token);
            localStorage.setItem("hasuraToken", res.htoken);
            localStorage.setItem("openid", res.openid);
            localStorage.setItem("socialUserId", res.socialUserId);
            localStorage.setItem("socialType", res.socialType);
            getTemplateInfo();
          } else {
            console.error("登录失败: 未返回 token");
          }
        });
      } catch (error) {
        console.error(error, "登录失败pc");
      }
    };
    const toAgreement = () => {
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_el_button = resolveComponent("el-button");
      const _component_el_collapse = resolveComponent("el-collapse");
      const _component_el_collapse_item = resolveComponent("el-collapse-item");
      const _component_el_icon = resolveComponent("el-icon");
      const _component_client_only = __nuxt_component_0;
      const _component_v_md_preview = resolveComponent("v-md-preview");
      const _component_el_dialog = resolveComponent("el-dialog");
      _push(`<!--[--><div class="bg-[#f3f8fa] p-2 overflow-auto" style="${ssrRenderStyle({ "height": "calc(100vh - 190px)" })}" data-v-93580b51><div class="pc_container" style="${ssrRenderStyle({ "display": "flex" })}" data-v-93580b51>`);
      if (unref(isNormal)) {
        _push(`<!--[--><div class="bg-[#fff] m_bg" style="${ssrRenderStyle({ "border-radius": "10px", "width": "40%", "height": "calc(100vh - 190px)" })}" data-v-93580b51><!--[-->`);
        ssrRenderList(unref(userInputForm), (item, index2) => {
          _push(`<div class="flex" data-v-93580b51><!--[-->`);
          ssrRenderList(item, (value, key) => {
            _push(ssrRenderComponent(_sfc_main$1, {
              type: key,
              onPayShowStatus: payShowChange,
              label: value == null ? void 0 : value.label,
              value: unref(inputs)[value == null ? void 0 : value.variable],
              required: value == null ? void 0 : value.required,
              placeholder: value == null ? void 0 : value.label,
              max_length: value == null ? void 0 : value.max_length,
              options: value == null ? void 0 : value.options,
              fileVerify: value == null ? void 0 : value.allowed_file_types,
              currentItem: props.currentItem,
              "onUpdate:value": (newValue) => unref(inputs)[value == null ? void 0 : value.variable] = newValue,
              ref_for: true,
              ref_key: "childRef",
              ref: childRef
            }, null, _parent));
          });
          _push(`<!--]--></div>`);
        });
        _push(`<!--]--><div class="p-3" style="${ssrRenderStyle({ "display": "flex", "justify-content": "space-between" })}" data-v-93580b51>`);
        _push(ssrRenderComponent(_component_el_button, { onClick: reset }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("tool.clear"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("tool.clear")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(ssrRenderComponent(_component_el_button, {
          onClick: onExec,
          loading: unref(loadingBtn),
          type: "primary"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`${ssrInterpolate(_ctx.$t("tool.execute"))}`);
            } else {
              return [
                createTextVNode(toDisplayString(_ctx.$t("tool.execute")), 1)
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div></div><div class="pc_right bg-[#fff]" data-v-93580b51><div id="typing-area" data-v-93580b51>`);
        if (unref(decListNew).length > 0 || unref(displayedText) || unref(isShow)) {
          _push(`<div class="decContaniner nop bg-[#fff]" data-v-93580b51>`);
          _push(ssrRenderComponent(_component_el_collapse, {
            modelValue: unref(activeNames),
            "onUpdate:modelValue": ($event) => isRef(activeNames) ? activeNames.value = $event : null
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent(_component_el_collapse_item, { name: "1" }, {
                  title: withCtx((_2, _push3, _parent3, _scopeId2) => {
                    if (_push3) {
                      if (unref(loadingDecImg)) {
                        _push3(`<div class="img_box" data-v-93580b51${_scopeId2}><img${ssrRenderAttr("src", unref(loadingImg))} alt="loading" data-v-93580b51${_scopeId2}></div>`);
                      } else {
                        _push3(`<div class="icon" data-v-93580b51${_scopeId2}>`);
                        _push3(ssrRenderComponent(_component_el_icon, null, {
                          default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                            if (_push4) {
                              _push4(ssrRenderComponent(unref(SuccessFilled), null, null, _parent4, _scopeId3));
                            } else {
                              return [
                                createVNode(unref(SuccessFilled))
                              ];
                            }
                          }),
                          _: 1
                        }, _parent3, _scopeId2));
                        _push3(`</div>`);
                      }
                      _push3(` ${ssrInterpolate(_ctx.$t("tool.execution_progress"))}`);
                    } else {
                      return [
                        unref(loadingDecImg) ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "img_box"
                        }, [
                          createVNode("img", {
                            src: unref(loadingImg),
                            alt: "loading"
                          }, null, 8, ["src"])
                        ])) : (openBlock(), createBlock("div", {
                          key: 1,
                          class: "icon"
                        }, [
                          createVNode(_component_el_icon, null, {
                            default: withCtx(() => [
                              createVNode(unref(SuccessFilled))
                            ]),
                            _: 1
                          })
                        ])),
                        createTextVNode(" " + toDisplayString(_ctx.$t("tool.execution_progress")), 1)
                      ];
                    }
                  }),
                  default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                    if (_push3) {
                      _push3(`<!--[-->`);
                      ssrRenderList(unref(decListNew), (item, index2) => {
                        _push3(`<div class="process" data-v-93580b51${_scopeId2}><div class="process_text label_width" data-v-93580b51${_scopeId2}>${ssrInterpolate(item.title)}</div>    <span style="${ssrRenderStyle({ "color": "#36b15e" })}" class="${ssrRenderClass(!item.status ? "loading-text" : "")}" data-v-93580b51${_scopeId2}>${ssrInterpolate(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading"))}</span></div>`);
                      });
                      _push3(`<!--]-->`);
                    } else {
                      return [
                        (openBlock(true), createBlock(Fragment, null, renderList(unref(decListNew), (item, index2) => {
                          return openBlock(), createBlock("div", {
                            key: index2,
                            class: "process"
                          }, [
                            createVNode("div", { class: "process_text label_width" }, toDisplayString(item.title), 1),
                            createTextVNode("    "),
                            createVNode("span", {
                              style: { "color": "#36b15e" },
                              class: !item.status ? "loading-text" : ""
                            }, toDisplayString(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading")), 3)
                          ]);
                        }), 128))
                      ];
                    }
                  }),
                  _: 1
                }, _parent2, _scopeId));
                _push2(`<div data-v-93580b51${_scopeId}>`);
                if (unref(displayedText)) {
                  _push2(`<div class="process" data-v-93580b51${_scopeId}></div>`);
                } else {
                  _push2(`<!---->`);
                }
                _push2(`</div>`);
                if (unref(displayedText)) {
                  _push2(ssrRenderComponent(_component_el_collapse_item, { name: "2" }, {
                    title: withCtx((_2, _push3, _parent3, _scopeId2) => {
                      if (_push3) {
                        if (unref(processLoadingPng)) {
                          _push3(`<div class="img_box" data-v-93580b51${_scopeId2}><img${ssrRenderAttr("src", unref(loadingImg))} alt="loading" data-v-93580b51${_scopeId2}></div>`);
                        } else {
                          _push3(`<div class="icon" data-v-93580b51${_scopeId2}>`);
                          _push3(ssrRenderComponent(_component_el_icon, null, {
                            default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                              if (_push4) {
                                _push4(ssrRenderComponent(unref(SuccessFilled), null, null, _parent4, _scopeId3));
                              } else {
                                return [
                                  createVNode(unref(SuccessFilled))
                                ];
                              }
                            }),
                            _: 1
                          }, _parent3, _scopeId2));
                          _push3(`</div>`);
                        }
                        _push3(` ${ssrInterpolate(_ctx.$t("tool.reasoning_process"))}`);
                      } else {
                        return [
                          unref(processLoadingPng) ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "img_box"
                          }, [
                            createVNode("img", {
                              src: unref(loadingImg),
                              alt: "loading"
                            }, null, 8, ["src"])
                          ])) : (openBlock(), createBlock("div", {
                            key: 1,
                            class: "icon"
                          }, [
                            createVNode(_component_el_icon, null, {
                              default: withCtx(() => [
                                createVNode(unref(SuccessFilled))
                              ]),
                              _: 1
                            })
                          ])),
                          createTextVNode(" " + toDisplayString(_ctx.$t("tool.reasoning_process")), 1)
                        ];
                      }
                    }),
                    default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                      if (_push3) {
                        _push3(`<div class="process" data-v-93580b51${_scopeId2}><div class="process_text" data-v-93580b51${_scopeId2}>${ssrInterpolate(unref(displayedText))}</div></div>`);
                      } else {
                        return [
                          createVNode("div", { class: "process" }, [
                            createVNode("div", { class: "process_text" }, toDisplayString(unref(displayedText)), 1)
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent2, _scopeId));
                } else {
                  _push2(`<!---->`);
                }
              } else {
                return [
                  createVNode(_component_el_collapse_item, { name: "1" }, {
                    title: withCtx(() => [
                      unref(loadingDecImg) ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "img_box"
                      }, [
                        createVNode("img", {
                          src: unref(loadingImg),
                          alt: "loading"
                        }, null, 8, ["src"])
                      ])) : (openBlock(), createBlock("div", {
                        key: 1,
                        class: "icon"
                      }, [
                        createVNode(_component_el_icon, null, {
                          default: withCtx(() => [
                            createVNode(unref(SuccessFilled))
                          ]),
                          _: 1
                        })
                      ])),
                      createTextVNode(" " + toDisplayString(_ctx.$t("tool.execution_progress")), 1)
                    ]),
                    default: withCtx(() => [
                      (openBlock(true), createBlock(Fragment, null, renderList(unref(decListNew), (item, index2) => {
                        return openBlock(), createBlock("div", {
                          key: index2,
                          class: "process"
                        }, [
                          createVNode("div", { class: "process_text label_width" }, toDisplayString(item.title), 1),
                          createTextVNode("    "),
                          createVNode("span", {
                            style: { "color": "#36b15e" },
                            class: !item.status ? "loading-text" : ""
                          }, toDisplayString(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading")), 3)
                        ]);
                      }), 128))
                    ]),
                    _: 1
                  }),
                  createVNode("div", null, [
                    unref(displayedText) ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "process"
                    })) : createCommentVNode("", true)
                  ]),
                  unref(displayedText) ? (openBlock(), createBlock(_component_el_collapse_item, {
                    key: 0,
                    name: "2"
                  }, {
                    title: withCtx(() => [
                      unref(processLoadingPng) ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "img_box"
                      }, [
                        createVNode("img", {
                          src: unref(loadingImg),
                          alt: "loading"
                        }, null, 8, ["src"])
                      ])) : (openBlock(), createBlock("div", {
                        key: 1,
                        class: "icon"
                      }, [
                        createVNode(_component_el_icon, null, {
                          default: withCtx(() => [
                            createVNode(unref(SuccessFilled))
                          ]),
                          _: 1
                        })
                      ])),
                      createTextVNode(" " + toDisplayString(_ctx.$t("tool.reasoning_process")), 1)
                    ]),
                    default: withCtx(() => [
                      createVNode("div", { class: "process" }, [
                        createVNode("div", { class: "process_text" }, toDisplayString(unref(displayedText)), 1)
                      ])
                    ]),
                    _: 1
                  })) : createCommentVNode("", true)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
        if (unref(isSubscribe)) {
          _push(`<div data-v-93580b51>${ssrInterpolate(unref(answer))} `);
          _push(ssrRenderComponent(_component_el_button, {
            type: "text",
            onClick: handleOrder
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(_ctx.$t("market.subscribe"))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<div data-v-93580b51>`);
        if (unref(loadingPng)) {
          _push(`<img${ssrRenderAttr("src", unref(loadingImg))} alt="loading" class="spinner" data-v-93580b51>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(loadingPng)) {
          _push(`<span text type="primary" class="stop_btn" data-v-93580b51>${ssrInterpolate(_ctx.$t("tool.stopGeneration"))}</span>`);
        } else {
          _push(`<!---->`);
        }
        if (unref(isCopy)) {
          _push(`<img${ssrRenderAttr("src", unref(copy))} alt="" style="${ssrRenderStyle({ "width": "20px" })}" class="copy" data-v-93580b51>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div></div><!--]-->`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="mobile_container" data-v-93580b51>`);
      _push(ssrRenderComponent(unref(Tabs), {
        active: unref(activeName),
        shrink: "",
        "line-width": "20",
        onClickTab
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(unref(Tab), {
              title: "输入",
              name: "a"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<!--[-->`);
                  ssrRenderList(unref(userInputForm), (item, index2) => {
                    _push3(`<div class="flex" data-v-93580b51${_scopeId2}><!--[-->`);
                    ssrRenderList(item, (value, key) => {
                      _push3(ssrRenderComponent(_sfc_main$1, {
                        type: key,
                        onPayShowStatus: payShowChange,
                        label: value == null ? void 0 : value.label,
                        value: unref(inputs)[value == null ? void 0 : value.variable],
                        required: value == null ? void 0 : value.required,
                        placeholder: value == null ? void 0 : value.label,
                        max_length: value == null ? void 0 : value.max_length,
                        options: value == null ? void 0 : value.options,
                        fileVerify: value == null ? void 0 : value.allowed_file_types,
                        currentItem: props.currentItem,
                        "onUpdate:value": (newValue) => unref(inputs)[value == null ? void 0 : value.variable] = newValue,
                        ref_for: true,
                        ref_key: "childRef",
                        ref: childRef
                      }, null, _parent3, _scopeId2));
                    });
                    _push3(`<!--]--></div>`);
                  });
                  _push3(`<!--]--><div class="p-3" style="${ssrRenderStyle({ "display": "flex", "justify-content": "space-between" })}" data-v-93580b51${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_el_button, { onClick: reset }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`Clear`);
                      } else {
                        return [
                          createTextVNode("Clear")
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent(_component_el_button, {
                    onClick: ($event) => onExec(),
                    loading: unref(loadingBtn),
                    type: "primary"
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`Execute`);
                      } else {
                        return [
                          createTextVNode("Execute")
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    (openBlock(true), createBlock(Fragment, null, renderList(unref(userInputForm), (item, index2) => {
                      return openBlock(), createBlock("div", {
                        class: "flex",
                        key: index2
                      }, [
                        (openBlock(true), createBlock(Fragment, null, renderList(item, (value, key) => {
                          return openBlock(), createBlock(_sfc_main$1, {
                            key: value["variable"],
                            type: key,
                            onPayShowStatus: payShowChange,
                            label: value == null ? void 0 : value.label,
                            value: unref(inputs)[value == null ? void 0 : value.variable],
                            required: value == null ? void 0 : value.required,
                            placeholder: value == null ? void 0 : value.label,
                            max_length: value == null ? void 0 : value.max_length,
                            options: value == null ? void 0 : value.options,
                            fileVerify: value == null ? void 0 : value.allowed_file_types,
                            currentItem: props.currentItem,
                            "onUpdate:value": (newValue) => unref(inputs)[value == null ? void 0 : value.variable] = newValue,
                            ref_for: true,
                            ref_key: "childRef",
                            ref: childRef
                          }, null, 8, ["type", "label", "value", "required", "placeholder", "max_length", "options", "fileVerify", "currentItem", "onUpdate:value"]);
                        }), 128))
                      ]);
                    }), 128)),
                    createVNode("div", {
                      class: "p-3",
                      style: { "display": "flex", "justify-content": "space-between" }
                    }, [
                      createVNode(_component_el_button, { onClick: reset }, {
                        default: withCtx(() => [
                          createTextVNode("Clear")
                        ]),
                        _: 1
                      }),
                      createVNode(_component_el_button, {
                        onClick: ($event) => onExec(),
                        loading: unref(loadingBtn),
                        type: "primary"
                      }, {
                        default: withCtx(() => [
                          createTextVNode("Execute")
                        ]),
                        _: 1
                      }, 8, ["onClick", "loading"])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(unref(Tab), {
              title: "结果",
              name: "b",
              disabled: unref(isdisabled)
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="mobile_right" data-v-93580b51${_scopeId2}><div id="typing-area" data-v-93580b51${_scopeId2}>`);
                  if (unref(decList).length > 0 || unref(displayedText)) {
                    _push3(`<div class="decContaniner nop bg-[#fff]" data-v-93580b51${_scopeId2}>`);
                    _push3(ssrRenderComponent(_component_el_collapse, {
                      modelValue: unref(activeNames),
                      "onUpdate:modelValue": ($event) => isRef(activeNames) ? activeNames.value = $event : null
                    }, {
                      default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(ssrRenderComponent(_component_el_collapse_item, { name: "1" }, {
                            title: withCtx((_4, _push5, _parent5, _scopeId4) => {
                              if (_push5) {
                                if (unref(loadingDecImg)) {
                                  _push5(`<div class="img_box" data-v-93580b51${_scopeId4}><img${ssrRenderAttr("src", unref(loadingImg))} alt="loading" data-v-93580b51${_scopeId4}></div>`);
                                } else {
                                  _push5(`<div class="icon" data-v-93580b51${_scopeId4}>`);
                                  _push5(ssrRenderComponent(_component_el_icon, null, {
                                    default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                                      if (_push6) {
                                        _push6(ssrRenderComponent(unref(SuccessFilled), null, null, _parent6, _scopeId5));
                                      } else {
                                        return [
                                          createVNode(unref(SuccessFilled))
                                        ];
                                      }
                                    }),
                                    _: 1
                                  }, _parent5, _scopeId4));
                                  _push5(`</div>`);
                                }
                                _push5(` ${ssrInterpolate(_ctx.$t("tool.execution_progress"))}`);
                              } else {
                                return [
                                  unref(loadingDecImg) ? (openBlock(), createBlock("div", {
                                    key: 0,
                                    class: "img_box"
                                  }, [
                                    createVNode("img", {
                                      src: unref(loadingImg),
                                      alt: "loading"
                                    }, null, 8, ["src"])
                                  ])) : (openBlock(), createBlock("div", {
                                    key: 1,
                                    class: "icon"
                                  }, [
                                    createVNode(_component_el_icon, null, {
                                      default: withCtx(() => [
                                        createVNode(unref(SuccessFilled))
                                      ]),
                                      _: 1
                                    })
                                  ])),
                                  createTextVNode(" " + toDisplayString(_ctx.$t("tool.execution_progress")), 1)
                                ];
                              }
                            }),
                            default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                              if (_push5) {
                                _push5(`<!--[-->`);
                                ssrRenderList(unref(decList), (item, index2) => {
                                  _push5(`<div class="process" data-v-93580b51${_scopeId4}><div class="process_text label_width" data-v-93580b51${_scopeId4}>${ssrInterpolate(item.title)}</div>    <span style="${ssrRenderStyle({ "color": "#36b15e" })}" class="${ssrRenderClass(!item.status ? "loading-text" : "")}" data-v-93580b51${_scopeId4}>${ssrInterpolate(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading"))}</span></div>`);
                                });
                                _push5(`<!--]-->`);
                              } else {
                                return [
                                  (openBlock(true), createBlock(Fragment, null, renderList(unref(decList), (item, index2) => {
                                    return openBlock(), createBlock("div", {
                                      key: index2,
                                      class: "process"
                                    }, [
                                      createVNode("div", { class: "process_text label_width" }, toDisplayString(item.title), 1),
                                      createTextVNode("    "),
                                      createVNode("span", {
                                        style: { "color": "#36b15e" },
                                        class: !item.status ? "loading-text" : ""
                                      }, toDisplayString(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading")), 3)
                                    ]);
                                  }), 128))
                                ];
                              }
                            }),
                            _: 1
                          }, _parent4, _scopeId3));
                          _push4(`<div data-v-93580b51${_scopeId3}><div class="process" data-v-93580b51${_scopeId3}></div></div>`);
                          if (unref(displayedText)) {
                            _push4(ssrRenderComponent(_component_el_collapse_item, {
                              title: "推导过程",
                              name: "2"
                            }, {
                              title: withCtx((_4, _push5, _parent5, _scopeId4) => {
                                if (_push5) {
                                  if (unref(processLoadingPng)) {
                                    _push5(`<div class="img_box" data-v-93580b51${_scopeId4}><img${ssrRenderAttr("src", unref(loadingImg))} alt="loading" data-v-93580b51${_scopeId4}></div>`);
                                  } else {
                                    _push5(`<div class="icon" data-v-93580b51${_scopeId4}>`);
                                    _push5(ssrRenderComponent(_component_el_icon, null, {
                                      default: withCtx((_5, _push6, _parent6, _scopeId5) => {
                                        if (_push6) {
                                          _push6(ssrRenderComponent(unref(SuccessFilled), null, null, _parent6, _scopeId5));
                                        } else {
                                          return [
                                            createVNode(unref(SuccessFilled))
                                          ];
                                        }
                                      }),
                                      _: 1
                                    }, _parent5, _scopeId4));
                                    _push5(`</div>`);
                                  }
                                  _push5(` ${ssrInterpolate(_ctx.$t("tool.reasoning_process"))}`);
                                } else {
                                  return [
                                    unref(processLoadingPng) ? (openBlock(), createBlock("div", {
                                      key: 0,
                                      class: "img_box"
                                    }, [
                                      createVNode("img", {
                                        src: unref(loadingImg),
                                        alt: "loading"
                                      }, null, 8, ["src"])
                                    ])) : (openBlock(), createBlock("div", {
                                      key: 1,
                                      class: "icon"
                                    }, [
                                      createVNode(_component_el_icon, null, {
                                        default: withCtx(() => [
                                          createVNode(unref(SuccessFilled))
                                        ]),
                                        _: 1
                                      })
                                    ])),
                                    createTextVNode(" " + toDisplayString(_ctx.$t("tool.reasoning_process")), 1)
                                  ];
                                }
                              }),
                              default: withCtx((_4, _push5, _parent5, _scopeId4) => {
                                if (_push5) {
                                  _push5(`<div class="process" data-v-93580b51${_scopeId4}><div class="process_text" data-v-93580b51${_scopeId4}>${ssrInterpolate(unref(displayedText))}</div></div>`);
                                } else {
                                  return [
                                    createVNode("div", { class: "process" }, [
                                      createVNode("div", { class: "process_text" }, toDisplayString(unref(displayedText)), 1)
                                    ])
                                  ];
                                }
                              }),
                              _: 1
                            }, _parent4, _scopeId3));
                          } else {
                            _push4(`<!---->`);
                          }
                        } else {
                          return [
                            createVNode(_component_el_collapse_item, { name: "1" }, {
                              title: withCtx(() => [
                                unref(loadingDecImg) ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "img_box"
                                }, [
                                  createVNode("img", {
                                    src: unref(loadingImg),
                                    alt: "loading"
                                  }, null, 8, ["src"])
                                ])) : (openBlock(), createBlock("div", {
                                  key: 1,
                                  class: "icon"
                                }, [
                                  createVNode(_component_el_icon, null, {
                                    default: withCtx(() => [
                                      createVNode(unref(SuccessFilled))
                                    ]),
                                    _: 1
                                  })
                                ])),
                                createTextVNode(" " + toDisplayString(_ctx.$t("tool.execution_progress")), 1)
                              ]),
                              default: withCtx(() => [
                                (openBlock(true), createBlock(Fragment, null, renderList(unref(decList), (item, index2) => {
                                  return openBlock(), createBlock("div", {
                                    key: index2,
                                    class: "process"
                                  }, [
                                    createVNode("div", { class: "process_text label_width" }, toDisplayString(item.title), 1),
                                    createTextVNode("    "),
                                    createVNode("span", {
                                      style: { "color": "#36b15e" },
                                      class: !item.status ? "loading-text" : ""
                                    }, toDisplayString(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading")), 3)
                                  ]);
                                }), 128))
                              ]),
                              _: 1
                            }),
                            createVNode("div", null, [
                              createVNode("div", { class: "process" })
                            ]),
                            unref(displayedText) ? (openBlock(), createBlock(_component_el_collapse_item, {
                              key: 0,
                              title: "推导过程",
                              name: "2"
                            }, {
                              title: withCtx(() => [
                                unref(processLoadingPng) ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "img_box"
                                }, [
                                  createVNode("img", {
                                    src: unref(loadingImg),
                                    alt: "loading"
                                  }, null, 8, ["src"])
                                ])) : (openBlock(), createBlock("div", {
                                  key: 1,
                                  class: "icon"
                                }, [
                                  createVNode(_component_el_icon, null, {
                                    default: withCtx(() => [
                                      createVNode(unref(SuccessFilled))
                                    ]),
                                    _: 1
                                  })
                                ])),
                                createTextVNode(" " + toDisplayString(_ctx.$t("tool.reasoning_process")), 1)
                              ]),
                              default: withCtx(() => [
                                createVNode("div", { class: "process" }, [
                                  createVNode("div", { class: "process_text" }, toDisplayString(unref(displayedText)), 1)
                                ])
                              ]),
                              _: 1
                            })) : createCommentVNode("", true)
                          ];
                        }
                      }),
                      _: 1
                    }, _parent3, _scopeId2));
                    _push3(`</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(ssrRenderComponent(_component_client_only, null, {}, _parent3, _scopeId2));
                  if (unref(isSubscribe)) {
                    _push3(`<div data-v-93580b51${_scopeId2}>${ssrInterpolate(unref(answer))} `);
                    _push3(ssrRenderComponent(_component_el_button, {
                      type: "text",
                      onClick: handleOrder
                    }, {
                      default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(`${ssrInterpolate(_ctx.$t("market.subscribe"))}`);
                        } else {
                          return [
                            createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1)
                          ];
                        }
                      }),
                      _: 1
                    }, _parent3, _scopeId2));
                    _push3(`</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "mobile_right" }, [
                      createVNode("div", { id: "typing-area" }, [
                        unref(decList).length > 0 || unref(displayedText) ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "decContaniner nop bg-[#fff]"
                        }, [
                          createVNode(_component_el_collapse, {
                            modelValue: unref(activeNames),
                            "onUpdate:modelValue": ($event) => isRef(activeNames) ? activeNames.value = $event : null
                          }, {
                            default: withCtx(() => [
                              createVNode(_component_el_collapse_item, { name: "1" }, {
                                title: withCtx(() => [
                                  unref(loadingDecImg) ? (openBlock(), createBlock("div", {
                                    key: 0,
                                    class: "img_box"
                                  }, [
                                    createVNode("img", {
                                      src: unref(loadingImg),
                                      alt: "loading"
                                    }, null, 8, ["src"])
                                  ])) : (openBlock(), createBlock("div", {
                                    key: 1,
                                    class: "icon"
                                  }, [
                                    createVNode(_component_el_icon, null, {
                                      default: withCtx(() => [
                                        createVNode(unref(SuccessFilled))
                                      ]),
                                      _: 1
                                    })
                                  ])),
                                  createTextVNode(" " + toDisplayString(_ctx.$t("tool.execution_progress")), 1)
                                ]),
                                default: withCtx(() => [
                                  (openBlock(true), createBlock(Fragment, null, renderList(unref(decList), (item, index2) => {
                                    return openBlock(), createBlock("div", {
                                      key: index2,
                                      class: "process"
                                    }, [
                                      createVNode("div", { class: "process_text label_width" }, toDisplayString(item.title), 1),
                                      createTextVNode("    "),
                                      createVNode("span", {
                                        style: { "color": "#36b15e" },
                                        class: !item.status ? "loading-text" : ""
                                      }, toDisplayString(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading")), 3)
                                    ]);
                                  }), 128))
                                ]),
                                _: 1
                              }),
                              createVNode("div", null, [
                                createVNode("div", { class: "process" })
                              ]),
                              unref(displayedText) ? (openBlock(), createBlock(_component_el_collapse_item, {
                                key: 0,
                                title: "推导过程",
                                name: "2"
                              }, {
                                title: withCtx(() => [
                                  unref(processLoadingPng) ? (openBlock(), createBlock("div", {
                                    key: 0,
                                    class: "img_box"
                                  }, [
                                    createVNode("img", {
                                      src: unref(loadingImg),
                                      alt: "loading"
                                    }, null, 8, ["src"])
                                  ])) : (openBlock(), createBlock("div", {
                                    key: 1,
                                    class: "icon"
                                  }, [
                                    createVNode(_component_el_icon, null, {
                                      default: withCtx(() => [
                                        createVNode(unref(SuccessFilled))
                                      ]),
                                      _: 1
                                    })
                                  ])),
                                  createTextVNode(" " + toDisplayString(_ctx.$t("tool.reasoning_process")), 1)
                                ]),
                                default: withCtx(() => [
                                  createVNode("div", { class: "process" }, [
                                    createVNode("div", { class: "process_text" }, toDisplayString(unref(displayedText)), 1)
                                  ])
                                ]),
                                _: 1
                              })) : createCommentVNode("", true)
                            ]),
                            _: 1
                          }, 8, ["modelValue", "onUpdate:modelValue"])
                        ])) : createCommentVNode("", true),
                        createVNode(_component_client_only, null, {
                          default: withCtx(() => [
                            unref(answer) && !unref(isSubscribe) ? (openBlock(), createBlock(_component_v_md_preview, {
                              key: 0,
                              text: unref(answer),
                              id: "previewMd"
                            }, null, 8, ["text"])) : createCommentVNode("", true)
                          ]),
                          _: 1
                        }),
                        unref(isSubscribe) ? (openBlock(), createBlock("div", { key: 1 }, [
                          createTextVNode(toDisplayString(unref(answer)) + " ", 1),
                          createVNode(_component_el_button, {
                            type: "text",
                            onClick: handleOrder
                          }, {
                            default: withCtx(() => [
                              createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1)
                            ]),
                            _: 1
                          })
                        ])) : createCommentVNode("", true)
                      ])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(unref(Tab), {
                title: "输入",
                name: "a"
              }, {
                default: withCtx(() => [
                  (openBlock(true), createBlock(Fragment, null, renderList(unref(userInputForm), (item, index2) => {
                    return openBlock(), createBlock("div", {
                      class: "flex",
                      key: index2
                    }, [
                      (openBlock(true), createBlock(Fragment, null, renderList(item, (value, key) => {
                        return openBlock(), createBlock(_sfc_main$1, {
                          key: value["variable"],
                          type: key,
                          onPayShowStatus: payShowChange,
                          label: value == null ? void 0 : value.label,
                          value: unref(inputs)[value == null ? void 0 : value.variable],
                          required: value == null ? void 0 : value.required,
                          placeholder: value == null ? void 0 : value.label,
                          max_length: value == null ? void 0 : value.max_length,
                          options: value == null ? void 0 : value.options,
                          fileVerify: value == null ? void 0 : value.allowed_file_types,
                          currentItem: props.currentItem,
                          "onUpdate:value": (newValue) => unref(inputs)[value == null ? void 0 : value.variable] = newValue,
                          ref_for: true,
                          ref_key: "childRef",
                          ref: childRef
                        }, null, 8, ["type", "label", "value", "required", "placeholder", "max_length", "options", "fileVerify", "currentItem", "onUpdate:value"]);
                      }), 128))
                    ]);
                  }), 128)),
                  createVNode("div", {
                    class: "p-3",
                    style: { "display": "flex", "justify-content": "space-between" }
                  }, [
                    createVNode(_component_el_button, { onClick: reset }, {
                      default: withCtx(() => [
                        createTextVNode("Clear")
                      ]),
                      _: 1
                    }),
                    createVNode(_component_el_button, {
                      onClick: ($event) => onExec(),
                      loading: unref(loadingBtn),
                      type: "primary"
                    }, {
                      default: withCtx(() => [
                        createTextVNode("Execute")
                      ]),
                      _: 1
                    }, 8, ["onClick", "loading"])
                  ])
                ]),
                _: 1
              }),
              createVNode(unref(Tab), {
                title: "结果",
                name: "b",
                disabled: unref(isdisabled)
              }, {
                default: withCtx(() => [
                  createVNode("div", { class: "mobile_right" }, [
                    createVNode("div", { id: "typing-area" }, [
                      unref(decList).length > 0 || unref(displayedText) ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "decContaniner nop bg-[#fff]"
                      }, [
                        createVNode(_component_el_collapse, {
                          modelValue: unref(activeNames),
                          "onUpdate:modelValue": ($event) => isRef(activeNames) ? activeNames.value = $event : null
                        }, {
                          default: withCtx(() => [
                            createVNode(_component_el_collapse_item, { name: "1" }, {
                              title: withCtx(() => [
                                unref(loadingDecImg) ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "img_box"
                                }, [
                                  createVNode("img", {
                                    src: unref(loadingImg),
                                    alt: "loading"
                                  }, null, 8, ["src"])
                                ])) : (openBlock(), createBlock("div", {
                                  key: 1,
                                  class: "icon"
                                }, [
                                  createVNode(_component_el_icon, null, {
                                    default: withCtx(() => [
                                      createVNode(unref(SuccessFilled))
                                    ]),
                                    _: 1
                                  })
                                ])),
                                createTextVNode(" " + toDisplayString(_ctx.$t("tool.execution_progress")), 1)
                              ]),
                              default: withCtx(() => [
                                (openBlock(true), createBlock(Fragment, null, renderList(unref(decList), (item, index2) => {
                                  return openBlock(), createBlock("div", {
                                    key: index2,
                                    class: "process"
                                  }, [
                                    createVNode("div", { class: "process_text label_width" }, toDisplayString(item.title), 1),
                                    createTextVNode("    "),
                                    createVNode("span", {
                                      style: { "color": "#36b15e" },
                                      class: !item.status ? "loading-text" : ""
                                    }, toDisplayString(item.status ? _ctx.$t("tool.completed") : _ctx.$t("tool.loading")), 3)
                                  ]);
                                }), 128))
                              ]),
                              _: 1
                            }),
                            createVNode("div", null, [
                              createVNode("div", { class: "process" })
                            ]),
                            unref(displayedText) ? (openBlock(), createBlock(_component_el_collapse_item, {
                              key: 0,
                              title: "推导过程",
                              name: "2"
                            }, {
                              title: withCtx(() => [
                                unref(processLoadingPng) ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "img_box"
                                }, [
                                  createVNode("img", {
                                    src: unref(loadingImg),
                                    alt: "loading"
                                  }, null, 8, ["src"])
                                ])) : (openBlock(), createBlock("div", {
                                  key: 1,
                                  class: "icon"
                                }, [
                                  createVNode(_component_el_icon, null, {
                                    default: withCtx(() => [
                                      createVNode(unref(SuccessFilled))
                                    ]),
                                    _: 1
                                  })
                                ])),
                                createTextVNode(" " + toDisplayString(_ctx.$t("tool.reasoning_process")), 1)
                              ]),
                              default: withCtx(() => [
                                createVNode("div", { class: "process" }, [
                                  createVNode("div", { class: "process_text" }, toDisplayString(unref(displayedText)), 1)
                                ])
                              ]),
                              _: 1
                            })) : createCommentVNode("", true)
                          ]),
                          _: 1
                        }, 8, ["modelValue", "onUpdate:modelValue"])
                      ])) : createCommentVNode("", true),
                      createVNode(_component_client_only, null, {
                        default: withCtx(() => [
                          unref(answer) && !unref(isSubscribe) ? (openBlock(), createBlock(_component_v_md_preview, {
                            key: 0,
                            text: unref(answer),
                            id: "previewMd"
                          }, null, 8, ["text"])) : createCommentVNode("", true)
                        ]),
                        _: 1
                      }),
                      unref(isSubscribe) ? (openBlock(), createBlock("div", { key: 1 }, [
                        createTextVNode(toDisplayString(unref(answer)) + " ", 1),
                        createVNode(_component_el_button, {
                          type: "text",
                          onClick: handleOrder
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(_ctx.$t("market.subscribe")), 1)
                          ]),
                          _: 1
                        })
                      ])) : createCommentVNode("", true)
                    ])
                  ])
                ]),
                _: 1
              }, 8, ["disabled"])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div>`);
      if (unref(payShow)) {
        _push(ssrRenderComponent(_component_el_dialog, {
          modelValue: unref(payShow),
          "onUpdate:modelValue": ($event) => isRef(payShow) ? payShow.value = $event : null,
          class: "payPC",
          "show-close": false
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(pay, {
                userInfo: unref(userInfo),
                currentItem: unref(currentItem),
                onToAgreement: toAgreement,
                subStatusDetail: unref(subStatusDetail),
                onClose: close,
                onSubscribe: subscribe
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode(pay, {
                  userInfo: unref(userInfo),
                  currentItem: unref(currentItem),
                  onToAgreement: toAgreement,
                  subStatusDetail: unref(subStatusDetail),
                  onClose: close,
                  onSubscribe: subscribe
                }, null, 8, ["userInfo", "currentItem", "subStatusDetail"])
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      if (unref(payShow)) {
        _push(ssrRenderComponent(unref(Popup), {
          show: unref(payShow),
          "onUpdate:show": ($event) => isRef(payShow) ? payShow.value = $event : null,
          round: "",
          closeable: "",
          class: "payMobile",
          position: "bottom",
          style: { height: "90%" }
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(ssrRenderComponent(payMobile, {
                userInfo: unref(userInfo),
                subStatusDetail: unref(subStatusDetail),
                currentItem: unref(currentItem),
                onToAgreement: toAgreement,
                onClose: close
              }, null, _parent2, _scopeId));
            } else {
              return [
                createVNode(payMobile, {
                  userInfo: unref(userInfo),
                  subStatusDetail: unref(subStatusDetail),
                  currentItem: unref(currentItem),
                  onToAgreement: toAgreement,
                  onClose: close
                }, null, 8, ["userInfo", "subStatusDetail", "currentItem"])
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div><footer class="text-gray-400 w-full bottom-0 text-sm text-center h-8 leading-8" data-v-93580b51> 内容由 AI 生成, 仅供参考 </footer><!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/tool/[appUuid].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _appUuid_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-93580b51"]]);

export { _appUuid_ as default };
//# sourceMappingURL=_appUuid_.vue2.mjs.map
